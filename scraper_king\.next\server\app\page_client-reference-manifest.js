globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/trpc/react.tsx <module evaluation>":{"id":"[project]/src/trpc/react.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/trpc/react.tsx":{"id":"[project]/src/trpc/react.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js","/_next/static/chunks/_43ef4566._.js","/_next/static/chunks/src_app_page_tsx_0b69dad7._.js"],"async":false},"[project]/node_modules/next/dist/client/app-dir/link.js":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js","/_next/static/chunks/_43ef4566._.js","/_next/static/chunks/src_app_page_tsx_0b69dad7._.js"],"async":false},"[project]/src/app/_components/post.tsx <module evaluation>":{"id":"[project]/src/app/_components/post.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js","/_next/static/chunks/_43ef4566._.js","/_next/static/chunks/src_app_page_tsx_0b69dad7._.js"],"async":false},"[project]/src/app/_components/post.tsx":{"id":"[project]/src/app/_components/post.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js","/_next/static/chunks/_43ef4566._.js","/_next/static/chunks/src_app_page_tsx_0b69dad7._.js"],"async":false},"[project]/node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js <module evaluation>":{"id":"[project]/node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js","/_next/static/chunks/_43ef4566._.js","/_next/static/chunks/src_app_page_tsx_0b69dad7._.js"],"async":false},"[project]/node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js":{"id":"[project]/node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_c45ff6dc._.js","/_next/static/chunks/src_trpc_c5525986._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js","/_next/static/chunks/_43ef4566._.js","/_next/static/chunks/src_app_page_tsx_0b69dad7._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_4a1c150e._.js","server/chunks/ssr/[root-of-the-server]__dec9bec1._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_4a1c150e._.js","server/chunks/ssr/[root-of-the-server]__dec9bec1._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_4a1c150e._.js","server/chunks/ssr/[root-of-the-server]__dec9bec1._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_4a1c150e._.js","server/chunks/ssr/[root-of-the-server]__dec9bec1._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_4a1c150e._.js","server/chunks/ssr/[root-of-the-server]__dec9bec1._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_4a1c150e._.js","server/chunks/ssr/[root-of-the-server]__dec9bec1._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_4a1c150e._.js","server/chunks/ssr/[root-of-the-server]__dec9bec1._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_4a1c150e._.js","server/chunks/ssr/[root-of-the-server]__dec9bec1._.js"],"async":false}},"[project]/src/trpc/react.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/trpc/react.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_4a1c150e._.js","server/chunks/ssr/[root-of-the-server]__dec9bec1._.js"],"async":false}},"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_4a1c150e._.js","server/chunks/ssr/[root-of-the-server]__dec9bec1._.js","server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_4fdb2def._.js","server/chunks/ssr/node_modules_cd309e94._.js","server/chunks/ssr/src_app__components_post_tsx_f3bf30c7._.js"],"async":false}},"[project]/src/app/_components/post.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/_components/post.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_4a1c150e._.js","server/chunks/ssr/[root-of-the-server]__dec9bec1._.js","server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_4fdb2def._.js","server/chunks/ssr/node_modules_cd309e94._.js","server/chunks/ssr/src_app__components_post_tsx_f3bf30c7._.js"],"async":false}},"[project]/node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_4a1c150e._.js","server/chunks/ssr/[root-of-the-server]__dec9bec1._.js","server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_4fdb2def._.js","server/chunks/ssr/node_modules_cd309e94._.js","server/chunks/ssr/src_app__components_post_tsx_f3bf30c7._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/trpc/react.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/trpc/react.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/app/_components/post.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/_components/post.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/layout":[{"path":"static/chunks/[root-of-the-server]__698e6b9e._.css","inlined":false}],"[project]/src/app/page":[{"path":"static/chunks/[root-of-the-server]__698e6b9e._.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/layout":["static/chunks/node_modules_c45ff6dc._.js","static/chunks/src_trpc_c5525986._.js","static/chunks/src_app_layout_tsx_c0237562._.js"],"[project]/src/app/page":["static/chunks/node_modules_c45ff6dc._.js","static/chunks/src_trpc_c5525986._.js","static/chunks/src_app_layout_tsx_c0237562._.js","static/chunks/_43ef4566._.js","static/chunks/src_app_page_tsx_0b69dad7._.js"]}}
